import os
import socket
import time
import threading
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS
from waitress import serve
import logging
from typing import Dict, Optional, Tuple, List
import uuid
import requests
import json

# 可选依赖 - 如果没有安装则跳过图像处理功能
try:
    import cv2
    import numpy as np
    from PIL import ImageGrab
    import pyautogui
    import pyperclip
    HAS_IMAGE_PROCESSING = True
    print("✅ 图像处理依赖已加载")
except ImportError as e:
    HAS_IMAGE_PROCESSING = False
    print(f"⚠️ 图像处理依赖未安装: {e}")
    print("⚠️ 将跳过UI自动化功能，仅提供API服务")

class Config:
    TEMPLATE_IMAGES = {
        "category_insight": "template_category.png",
        "store_report_link": "template_store_link.png",
        "product_report_link": "template_product_link.png",
        "run_button": "template_run_button.png",
        "category_dialog": "template_category_dialog.png",
        "store_dialog": "template_store_dialog.png",
        "product_dialog": "template_product_dialog.png",
        "input_field": "template_input_field.png",
        "confirm_button": "template_confirm_button.png",
        "completed_dialog": "template_completed_dialog.png",
        "close_button": "template_close_button.png",
        "success_indicator": "template_success.png",
        "runtime_error": "template_runtime_error.png",
        "task_id_input": "template_task_id_input.png",
        "error_window": "error_window.png",  # 错误窗口特征图片
        "confirm_close": "confirm_close.png",  # 确认关闭按钮
        "server_port_input": "template_server_port.png"  # 新增服务器端口输入框模板
    }


    SHADOWBOT_PATH = r"C:\ShadowBot\ShadowBot.exe"  # 程序路径
    FAILURE_RECOVERY_TIMEOUT = 10  # 失败恢复超时时间(秒)
    SCREENSHOT_DIR = "screenshots"
    TEMPLATE_DIR = "templates"
    MATCH_THRESHOLD = 0.85
    MOUSE_MOVE_DELAY = 0.2
    DIALOG_WAIT_TIME = 60.0
    COMPLETED_WAIT_TIME = 300.0
    POLL_INTERVAL = 0.5
    MAX_POLL_ATTEMPTS = int(DIALOG_WAIT_TIME / POLL_INTERVAL)
    RUN_BUTTON_WAIT_TIME = 1.0
    COMPLETED_POLL_INTERVAL = 5.0
    API_KEY = os.getenv("API_KEY", "shadowbot2023")
    ERROR_RETRY_COUNT = 3

    # 服务器配置
    SERVER_ID = os.getenv("SERVER_ID", socket.gethostname())  # 当前服务器唯一标识

class ImageProcessingService:
    def __init__(self):
        print("🔧 初始化ImageProcessingService...")
        self.logger = logging.getLogger("ImageProcessing")
        self._handling_error = False  # 错误处理状态标志
        self._ui_lock = threading.Lock()  # UI自动化互斥锁，防止并发冲突

        if HAS_IMAGE_PROCESSING:
            print("   ✅ 图像处理依赖可用，初始化完整功能")
            try:
                self._validate_templates()
                print("   ✅ 模板验证完成")
            except Exception as e:
                print(f"   ⚠️ 模板验证失败: {e}")

            try:
                self.templates = self._load_templates()
                print(f"   ✅ 加载了 {len(self.templates)} 个模板")
            except Exception as e:
                print(f"   ❌ 模板加载失败: {e}")
                self.templates = {}

            try:
                self._setup_autogui()
                print("   ✅ pyautogui设置完成")
            except Exception as e:
                print(f"   ⚠️ pyautogui设置失败: {e}")

            self.logger.info("图像处理服务初始化完成")
        else:
            print("   ⚠️ 图像处理依赖缺失，使用模拟模式")
            self.templates = {}
            self.logger.warning("图像处理依赖缺失，UI自动化功能不可用")

        print("   ✅ ImageProcessingService初始化完成")


    def _check_and_close_status(self):
        try:
            screenshot = np.array(ImageGrab.grab())
            screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

            # 检查是否有成功或失败弹窗
            success_pos = self._match_template("success_indicator", screenshot)
            error_pos = self._match_template("runtime_error", screenshot)

            if success_pos or error_pos:
                self.logger.info("检测到成功/失败弹窗，尝试关闭")
                close_pos = self._match_template("close_button", screenshot)
                if close_pos:
                    close_x, close_y = self._get_center(close_pos, "close_button")
                    self._click_at_position(close_x, close_y)
                    time.sleep(1)  # 关闭后等待一会儿
                    return True
        except Exception as e:
            self.logger.error(f"检查状态弹窗时出错: {str(e)}")
        return False


    def _setup_autogui(self):
        if HAS_IMAGE_PROCESSING:
            pyautogui.PAUSE = 0.5
            pyautogui.FAILSAFE = False

    def _validate_templates(self):
        if not HAS_IMAGE_PROCESSING:
            self.logger.warning("跳过模板验证（图像处理依赖缺失）")
            return

        missing = []
        corrupted = []

        if not hasattr(self, 'logger'):
            self.logger = logging.getLogger("ImageProcessing")

        for name, filename in Config.TEMPLATE_IMAGES.items():
            path = os.path.join(Config.TEMPLATE_DIR, filename)

            if not os.path.exists(path):
                missing.append((name, filename))
                continue

            try:
                img = cv2.imread(path, cv2.IMREAD_COLOR)
                if img is None:
                    corrupted.append((name, filename))
            except Exception as e:
                corrupted.append((name, filename))

        if missing or corrupted:
            error_msg = "模板验证失败:\n"
            if missing:
                error_msg += "缺少以下模板文件:\n" + "\n".join([f"{name}: {file}" for name, file in missing]) + "\n"
            if corrupted:
                error_msg += "以下模板文件损坏:\n" + "\n".join([f"{name}: {file}" for name, file in corrupted])
            self.logger.error(error_msg)
            raise FileNotFoundError(error_msg)

    def _load_templates(self) -> Dict[str, np.ndarray]:
        templates = {}
        for name, filename in Config.TEMPLATE_IMAGES.items():
            path = os.path.join(Config.TEMPLATE_DIR, filename)
            template = cv2.imread(path, cv2.IMREAD_COLOR)
            if template is None:
                raise ValueError(f"无法加载模板图像: {path}")
            templates[name] = template
        return templates

    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l_channel, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        cl = clahe.apply(l_channel)
        limg = cv2.merge((cl,a,b))
        return cv2.cvtColor(limg, cv2.COLOR_LAB2BGR)

    def _match_template(self, template_name: str, screenshot: np.ndarray, multi_scale: bool = False) -> Optional[Tuple[Tuple[int,int], float, float]]:
        try:
            processed_screen = self._preprocess_image(screenshot)
            template = self.templates[template_name]
            processed_template = self._preprocess_image(template)

            best_match = None
            scales = [0.8,0.9,1.0,1.1,1.2] if multi_scale else [1.0]

            for scale in scales:
                resized = cv2.resize(
                    processed_template,
                    (int(template.shape[1]*scale),
                     int(template.shape[0]*scale))
                )

                res = cv2.matchTemplate(
                    processed_screen,
                    resized,
                    cv2.TM_CCOEFF_NORMED
                )

                _, max_val, _, max_loc = cv2.minMaxLoc(res)

                if best_match is None or max_val > best_match[1]:
                    best_match = (max_loc, max_val, scale)

            return best_match if best_match and best_match[1] >= Config.MATCH_THRESHOLD else None

        except Exception as e:
            self.logger.error(f"模板[{template_name}]匹配错误: {str(e)}", exc_info=True)
            return None

    def _detect_error(self, screenshot: np.ndarray = None) -> bool:
        """检测是否有运行错误"""
        if self._handling_error:  # 如果正在处理错误，不再重复检测
            return False

        try:
            if screenshot is None:
                screenshot = np.array(ImageGrab.grab())
                screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

            # 检查是否有错误弹窗
            error_pos = self._match_template("runtime_error", screenshot)
            if error_pos:
                self.logger.warning("检测到运行错误弹窗")
                return True

            return False
        except Exception as e:
            self.logger.error(f"错误检测失败: {str(e)}")
            return False

    def _handle_error(self, max_attempts: int = 3) -> bool:
        """尝试处理错误状态"""
        if self._handling_error:  # 防止重入
            return False

        self._handling_error = True
        attempt = 0
        result = False

        try:
            while attempt < max_attempts:
                attempt += 1
                self.logger.info(f"开始错误处理(尝试 {attempt}/{max_attempts})...")

                try:
                    # 第一次检测错误
                    screenshot = np.array(ImageGrab.grab())
                    screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

                    if not self._match_template("runtime_error", screenshot):
                        self.logger.info("错误弹窗已消失")
                        result = True
                        break

                    # 添加随机延迟，避免过快执行
                    time.sleep(np.random.uniform(1.0, 2.0))

                    # 尝试点击关闭按钮
                    close_pos = self._match_template("close_button", screenshot)
                    if close_pos:
                        close_x, close_y = self._get_center(close_pos, "close_button")
                        if self._click_at_position(close_x, close_y):
                            self.logger.info("已点击关闭按钮尝试恢复")
                            # 点击后等待一段时间确认错误已解决
                            time.sleep(2.0)
                            # 检查错误是否真的解决了
                            if not self._detect_error():
                                result = True
                                break
                            continue

                    # 如果找不到关闭按钮，尝试点击完成对话框的关闭按钮
                    completed_pos = self._match_template("completed_dialog", screenshot)
                    if completed_pos:
                        completed_x, completed_y = self._get_center(completed_pos, "completed_dialog")
                        if self._click_at_position(completed_x, completed_y):
                            self.logger.info("已点击完成对话框尝试恢复")
                            time.sleep(2.0)
                            if not self._detect_error():
                                result = True
                                break
                            continue

                    # 如果以上方法都失败，尝试按ESC键
                    pyautogui.press('esc')
                    self.logger.info("尝试按ESC键关闭弹窗")
                    time.sleep(2.0)
                    if not self._detect_error():
                        result = True
                        break

                except Exception as e:
                    self.logger.error(f"错误处理尝试{attempt}失败: {str(e)}")
                    time.sleep(1.0)

            if not result:
                self.logger.warning(f"所有{max_attempts}次错误处理方法均失败")

            return result
        finally:
            self._handling_error = False

    def _click_at_position(self, x: int, y: int, clicks: int = 1, button: str = 'left') -> bool:
        try:
            offset_x = np.random.randint(-5,5)
            offset_y = np.random.randint(-5,5)

            pyautogui.moveTo(
                x + offset_x,
                y + offset_y,
                duration=Config.MOUSE_MOVE_DELAY
            )
            pyautogui.click(clicks=clicks, button=button,interval=0.1)
            time.sleep(0.5)  # 增加点击后的延迟
            return True
        except Exception as e:
            self.logger.error(f"点击({x},{y})失败: {str(e)}")
            return False

    def _type_text(self, text: str, retry: int = 3) -> bool:
        for attempt in range(retry):
            try:
                # 先保存当前剪贴板内容
                original_clipboard = pyperclip.paste()

                pyautogui.hotkey('ctrl','a')
                pyautogui.press('backspace')
                time.sleep(0.1)

                # 备份剪贴板后写入新内容
                try:
                    pyperclip.copy(text)
                    time.sleep(0.2)  # 确保复制完成
                    pyautogui.hotkey('ctrl','v')
                    time.sleep(0.3)

                    # 验证是否粘贴成功
                    if pyperclip.paste() == text:
                        return True
                finally:
                    # 恢复剪贴板内容
                    pyperclip.copy(original_clipboard)

                # 如果粘贴失败，尝试直接输入
                pyautogui.write(str(text), interval=0.05)
                return True

            except Exception as e:
                self.logger.warning(f"文本输入失败(尝试{attempt+1}/{retry}): {str(e)}")
                time.sleep(0.5)

        self.logger.error(f"无法输入文本: {text}")
        return False

    def _get_center(self, match_result: Tuple[Tuple[int,int],float,float], template_name: str) -> Tuple[int,int]:
        (x,y), _, scale = match_result
        template = self.templates[template_name]
        return (
            x + int(template.shape[1]*scale)//2,
            y + int(template.shape[0]*scale)//2
        )

    def process_report(self, report_type: str, data: dict) -> dict:
        """处理报告请求 - 支持真实UI自动化和模拟模式"""
        self.logger.info(f"开始处理报告: {report_type}")
        self.logger.info(f"请求数据: {data}")

        # 检查是否有图像处理依赖
        if not HAS_IMAGE_PROCESSING:
            self.logger.warning("图像处理依赖缺失，使用模拟模式")
            return self._simulate_report_processing(report_type, data)

        # 🔒 使用UI锁确保串行执行，防止并发冲突
        with self._ui_lock:
            return self._process_report_internal(report_type, data)

    def _simulate_report_processing(self, report_type: str, data: dict) -> dict:
        """模拟报告处理（当图像处理依赖缺失时）"""
        task_id = data.get('task_id', 'unknown')
        request_data = data.get('requestData', {})

        # 模拟处理时间
        import time
        time.sleep(2)  # 模拟处理延迟

        self.logger.info(f"模拟处理完成: {task_id}")

        return {
            "success": True,
            "message": f"模拟处理完成 - {report_type}",
            "debug_image": "",
            "error_handled": False,
            "task_id": task_id,
            "report_type": report_type,
            "processed_data": request_data
        }

    def _process_report_internal(self, report_type: str, data: dict) -> dict:
        result = {
            "success": False,
            "message": "",
            "debug_image": "",
            "input_text": "",
            "is_completed": False,
            "confidence": 0.0,
            "error_handled": False
        }

        try:
            task_id = data.get('task_id', 'unknown')
            request_data = data.get('requestData', {})

            self.logger.info(f"🔒 获得UI锁，开始处理 {report_type} 报告，任务ID: {task_id}")
            self.logger.info(f"请求数据: {request_data}")

            # 提取具体的业务数据
            store_link = request_data.get('store_link', '')
            trigger_action = request_data.get('trigger_action', '')

            if report_type == 'store_report_link' and not store_link:
                result["message"] = "缺少store_link参数"
                return result

            # 新增：在开始前检查并关闭任何存在的成功/失败状态弹窗
            self._check_and_close_status()
            if not isinstance(report_type,str) or report_type not in Config.TEMPLATE_IMAGES:
                raise ValueError(f"无效的报告类型: {report_type}")

            # 开始前检查错误
            if self._detect_error():
                result["error_handled"] = self._handle_error()
                if not result["error_handled"]:
                    result["message"] = "开始处理前检测到运行错误且无法解决"
                    self.logger.warning(result["message"])
                    return result

            screenshot = np.array(ImageGrab.grab())
            screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)
            debug_images = [screenshot.copy()]

            self.logger.info(f"开始处理 {report_type} 报告")
            button_pos = self._match_template(report_type, screenshot)

            if not button_pos:
                result["message"] = f"未找到{report_type}按钮(最低置信度阈值: {Config.MATCH_THRESHOLD})"
                self.logger.warning(result["message"])
                return result

            center_x, center_y = self._get_center(button_pos, report_type)
            result["confidence"] = button_pos[1]

            if not self._click_at_position(center_x, center_y):
                result["message"] = "点击主按钮失败"
                self.logger.warning(result["message"])
                return result

            time.sleep(Config.RUN_BUTTON_WAIT_TIME)

            run_screen = np.array(ImageGrab.grab())
            run_screen = cv2.cvtColor(run_screen, cv2.COLOR_RGB2BGR)
            debug_images.append(run_screen.copy())

            run_pos = self._match_template("run_button", run_screen)
            if not run_pos:
                result["message"] = "未找到运行按钮"
                self.logger.warning(result["message"])
                return result

            run_x, run_y = self._get_center(run_pos, "run_button")
            if not self._click_at_position(run_x, run_y):
                result["message"] = "点击运行按钮失败"
                self.logger.warning(result["message"])
                return result

            dialog_type = report_type.split('_')[0]
            dialog_result = self._handle_dialog(dialog_type, data)
            if not dialog_result["success"]:
                result.update(dialog_result)
                return result

            result["is_completed"] = self._check_completion()
            result["success"] = result["is_completed"]
            result["message"] = "流程完成" if result["success"] else "流程可能未完成"

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            debug_path = os.path.join(Config.SCREENSHOT_DIR, f"debug_{report_type}_{timestamp}.png")
            cv2.imwrite(debug_path, np.vstack(debug_images))
            result["debug_image"] = debug_path

            self.logger.info(f"{report_type} 报告处理完成: {result['message']}")

        except Exception as e:
            error_msg = f"处理失败: {str(e)}"
            result["message"] = error_msg
            self.logger.error(error_msg, exc_info=True)

            # 出错时尝试处理错误状态
            result["error_handled"] = self._handle_error()

            if 'debug_images' in locals():
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                error_path = os.path.join(Config.SCREENSHOT_DIR, f"error_{report_type}_{timestamp}.png")
                cv2.imwrite(error_path, np.vstack(debug_images))
                result["debug_image"] = error_path

        return result

    def _handle_dialog(self, dialog_type: str, data: dict) -> dict:
        result = {
            "success": False,
            "message": "",
            "input_text": ""
        }

        try:
            task_id = data.get('task_id', 'unknown')
            self.logger.info(f"🔍 开始等待 {dialog_type} 弹窗，任务ID: {task_id}")

            dialog_pos = None
            start_time = time.time()
            check_count = 0

            while time.time() - start_time < Config.DIALOG_WAIT_TIME:
                check_count += 1

                # 每次循环检查错误
                if self._detect_error():
                    if not self._handle_error():
                        result["message"] = f"处理{dialog_type}弹窗时检测到运行错误"
                        break

                screen = np.array(ImageGrab.grab())
                screen = cv2.cvtColor(screen, cv2.COLOR_RGB2BGR)

                dialog_pos = self._match_template(f"{dialog_type}_dialog", screen)
                if dialog_pos:
                    self.logger.info(f"✅ 找到 {dialog_type} 弹窗，任务ID: {task_id}，检查次数: {check_count}")
                    break

                # 每10次检查输出一次日志，避免日志过多
                if check_count % 10 == 0:
                    elapsed = time.time() - start_time
                    self.logger.debug(f"⏳ 等待 {dialog_type} 弹窗中，任务ID: {task_id}，已等待: {elapsed:.1f}s")

                time.sleep(Config.POLL_INTERVAL)

            if not dialog_pos:
                elapsed = time.time() - start_time
                result["message"] = f"未找到{dialog_type}弹窗，任务ID: {task_id}，等待时间: {elapsed:.1f}s，检查次数: {check_count}"
                self.logger.warning(result["message"])
                return result


                    # 2. 先填写任务ID (新增部分)
            task_id_pos = self._match_template("task_id_input", screen)
            if not task_id_pos:
                result["message"] = "未找到任务ID输入框"
                self.logger.warning(result["message"])
                return result
            task_id_x, task_id_y = self._get_center(task_id_pos, "task_id_input")
            if not self._click_at_position(task_id_x, task_id_y):
                result["message"] = "点击任务ID输入框失败"
                self.logger.warning(result["message"])
                return result
            if not self._type_text(data.get("task_id", "")):  # 填充任务ID
                result["message"] = "输入任务ID失败"
                self.logger.warning(result["message"])
                return result


                   # 3. 新增：填写服务器端口
            server_port_pos = self._match_template("server_port_input", screen)
            if server_port_pos:  # 如果找到端口输入框
                port_x, port_y = self._get_center(server_port_pos, "server_port_input")
                if not self._click_at_position(port_x, port_y):
                    result["message"] = "点击服务器端口输入框失败"
                    return result

                # 获取目标服务器端口
                try:
                    from flask import request, has_request_context
                    if has_request_context():
                        target_server = request.headers.get("X-Target-Server") or "unknown"
                    else:
                        target_server = data.get("target_server", "unknown")
                except ImportError:
                    target_server = data.get("target_server", "unknown")

                server_port = target_server.split(":")[-1] if ":" in target_server else "7000"

                if not self._type_text(server_port):
                    result["message"] = "输入服务器端口失败"
                    return result


            input_pos = self._match_template("input_field", screen)
            if not input_pos:
                result["message"] = "未找到输入框"
                self.logger.warning(result["message"])
                return result

            input_x, input_y = self._get_center(input_pos, "input_field")
            if not self._click_at_position(input_x, input_y):
                result["message"] = "点击输入框失败"
                self.logger.warning(result["message"])
                return result


            # 提取真正的请求数据
            request_data = data.get('requestData', data)  # 兼容新旧数据结构

            # 获取对应字段
            field_name = f"{dialog_type}_link" if dialog_type in ['store', 'product'] else "category"

            text = request_data.get(field_name, "")
            if not text:
                self.logger.error(f"获取{dialog_type}输入内容失败! 原始数据: {data}")
                return {
                    "success": False,
                    "message": f"{field_name}内容不能为空",
                    "input_text": ""
                }

            self.logger.info(f"准备输入文本内容: {text}")

            if not self._type_text(text):
                result["message"] = "输入文本失败"
                self.logger.warning(result["message"])
                return result

            confirm_pos = self._match_template("confirm_button", screen)
            if not confirm_pos:
                result["message"] = "未找到确认按钮"
                self.logger.warning(result["message"])
                return result

            confirm_x, confirm_y = self._get_center(confirm_pos, "confirm_button")
            if not self._click_at_position(confirm_x, confirm_y):
                result["message"] = "点击确认按钮失败"
                self.logger.warning(result["message"])
                return result

            result.update({
                "success": True,
                "message": f"{dialog_type}弹窗处理成功",
                "input_text": text
            })

        except Exception as e:
            error_msg = f"处理{dialog_type}弹窗时出错: {str(e)}"
            result["message"] = error_msg
            self.logger.error(error_msg, exc_info=True)

        return result

    def _check_completion(self) -> bool:
        start_time = time.time()

        while time.time() - start_time < Config.COMPLETED_WAIT_TIME:
            # 每次检查完成状态前先检查错误
            if self._detect_error():
                if not self._handle_error():
                    return False

            screen = np.array(ImageGrab.grab())
            screen = cv2.cvtColor(screen, cv2.COLOR_RGB2BGR)

            success_pos = self._match_template("success_indicator", screen)
            if success_pos:
                close_pos = self._match_template("close_button", screen)
                if not close_pos:
                    close_pos = self._match_template("completed_dialog", screen)
                    template_name = "completed_dialog"
                else:
                    template_name = "close_button"

                if close_pos:
                    close_x, close_y = self._get_center(close_pos, template_name)
                    self._click_at_position(close_x, close_y)

                self.logger.info("检测到任务完成")
                return True

            time.sleep(Config.COMPLETED_POLL_INTERVAL)

        self.logger.warning("任务完成检查超时")
        return False

    def _recover_from_failure(self):
        """
        任务失败后恢复流程:
        1. 尝试定位并关闭错误窗口
        2. 确认关闭
        3. 重启ShadowBot程序
        """
        self.logger.info("开始执行失败恢复流程...")
        start_time = time.time()

        try:
            # 步骤1: 检测并关闭错误窗口
            error_closed = False
            while time.time() - start_time < Config.FAILURE_RECOVERY_TIMEOUT:
                screenshot = np.array(ImageGrab.grab())
                screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

                # 匹配错误窗口特征图片
                error_window_pos = self._match_template("error_window", screenshot)
                if error_window_pos:
                    # 计算关闭按钮位置(假设在右上角)
                    pos, confidence, scale = error_window_pos
                    template_h, template_w = self.templates["error_window"].shape[:2]

                    # 关闭按钮在窗口右上角(向右偏移窗口宽度-30像素，向上偏移20像素)
                    close_x = pos[0] + int(template_w * scale) - 30
                    close_y = pos[1] + 20

                    if self._click_at_position(close_x, close_y):
                        self.logger.info(f"已点击错误窗口关闭按钮({close_x},{close_y})")
                        error_closed = True
                        break

                time.sleep(0.5)  # 降低CPU使用率

            # 步骤2: 确认关闭(如果有确认对话框)
            if error_closed:
                confirm_start = time.time()
                while time.time() - confirm_start < 5:  # 给确认对话框5秒时间显示
                    screenshot = np.array(ImageGrab.grab())
                    screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)

                    confirm_pos = self._match_template("confirm_close", screenshot)
                    if confirm_pos:
                        confirm_x, confirm_y = self._get_center(confirm_pos, "confirm_close")
                        if self._click_at_position(confirm_x, confirm_y):
                            self.logger.info("已点击确认关闭按钮")
                            break
                    time.sleep(0.5)

            # 步骤3: 重启ShadowBot程序
            self.logger.info(f"正在启动程序: {Config.SHADOWBOT_PATH}")
            try:
                if os.path.exists(Config.SHADOWBOT_PATH):
                    os.startfile(Config.SHADOWBOT_PATH)
                    self.logger.info("程序启动命令已发送")
                    time.sleep(5)  # 等待程序启动
                else:
                    self.logger.error(f"程序路径不存在: {Config.SHADOWBOT_PATH}")
            except Exception as e:
                self.logger.error(f"启动程序失败: {str(e)}")

            return True

        except Exception as e:
            self.logger.error(f"恢复过程中出错: {str(e)}")
            return False












class TaskManager:
    """单例工作流管理器"""
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(TaskManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        print("🔧 初始化TaskManager单例...")

        # 防止重复初始化
        if hasattr(self, '_initialized'):
            print("   ⚠️ TaskManager已初始化，跳过")
            return

        print("   📝 设置基本属性")
        self._initialized = True
        self._workflow_lock = threading.Lock()
        self.logger = logging.getLogger("TaskManager")

        print("   📝 初始化工作流状态")
        # 当前工作流状态
        self.current_workflow = {
            "task_id": None,
            "status": "idle",  # idle, processing, waiting_url, completed, error
            "start_time": None,
            "end_time": None,
            "result_url": None,
            "error": None,
            "user_token": None,
            "report_type": None,
            "callback_url": None
        }

        # 工作流历史记录（保留最近10个）
        self.workflow_history = []

        print("   🖼️ 初始化图像处理服务")
        try:
            # 初始化图像处理服务
            self.image_service = ImageProcessingService()
            print("   ✅ 图像处理服务初始化成功")
        except Exception as e:
            print(f"   ❌ 图像处理服务初始化失败: {e}")
            raise

        self.logger.info("TaskManager 单例初始化完成")
        print("   ✅ TaskManager单例初始化完成")

    @classmethod
    def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def start_workflow(self, task_id: str, report_type: str, data: dict, user_token: str = None, callback_url: str = None):
        """启动工作流"""
        print(f"\n🔧 TaskManager.start_workflow 被调用")
        print(f"   参数: task_id={task_id}")
        print(f"   参数: report_type={report_type}")
        print(f"   参数: user_token={user_token}")
        print(f"   参数: callback_url={callback_url}")
        print(f"   参数: data keys={list(data.keys()) if data else 'None'}")

        try:
            print("🔒 获取工作流锁...")
            with self._workflow_lock:
                print("✅ 工作流锁获取成功")

                print("🔍 检查当前工作流状态...")
                print(f"   当前状态: {self.current_workflow['status']}")
                print(f"   当前任务ID: {self.current_workflow['task_id']}")

                # 检查是否有正在处理的工作流
                if self.current_workflow["status"] in ["processing", "waiting_url"]:
                    print("❌ 工作流正在处理中，拒绝新请求")
                    return {
                        "success": False,
                        "message": "工作流正在处理中，请稍后再试",
                        "current_task_id": self.current_workflow["task_id"],
                        "status": self.current_workflow["status"]
                    }

                print("📝 更新工作流状态...")
                # 启动新工作流
                self.current_workflow.update({
                    "task_id": task_id,
                    "status": "processing",
                    "start_time": datetime.now().isoformat(),
                    "end_time": None,
                    "result_url": None,
                    "error": None,
                    "user_token": user_token or "",  # 确保不是None
                    "report_type": report_type,
                    "callback_url": callback_url or ""  # 确保不是None
                })
                print("✅ 工作流状态更新完成")
                print(f"   新状态: {self.current_workflow}")

                print("🚀 启动后台处理线程...")
                # 在后台线程中处理工作流
                try:
                    thread = threading.Thread(
                        target=self._process_workflow,
                        args=(task_id, report_type, data),
                        daemon=True
                    )
                    thread.start()
                    print("✅ 后台线程启动成功")
                except Exception as e:
                    print(f"❌ 后台线程启动失败: {e}")
                    raise

                result = {
                    "success": True,
                    "message": "工作流已启动",
                    "task_id": task_id,
                    "status": "processing"
                }
                print(f"✅ start_workflow 成功返回: {result}")
                return result

        except Exception as e:
            print(f"❌ start_workflow 异常: {str(e)}")
            import traceback
            print(f"❌ 异常堆栈: {traceback.format_exc()}")
            return {
                "success": False,
                "message": f"启动工作流失败: {str(e)}",
                "task_id": task_id
            }

    def _process_workflow(self, task_id: str, report_type: str, data: dict):
        """在后台处理工作流"""
        print(f"\n🔄 _process_workflow 后台线程开始")
        print(f"   线程ID: {threading.current_thread().ident}")
        print(f"   task_id: {task_id}")
        print(f"   report_type: {report_type}")
        print(f"   data: {data}")

        try:
            print("📞 调用 image_service.process_report...")
            # 使用图像处理服务处理报告
            result = self.image_service.process_report(report_type, data)
            print(f"✅ process_report 返回结果: {result}")

            print("🔒 获取工作流锁以更新状态...")
            with self._workflow_lock:
                print("✅ 工作流锁获取成功，更新状态...")

                if result["success"]:
                    print("✅ 处理成功，设置状态为 waiting_url")
                    # 处理成功，等待URL提交
                    self.current_workflow["status"] = "waiting_url"
                    print(f"✅ 工作流 {task_id} 处理成功，等待URL提交")
                else:
                    print("❌ 处理失败，设置状态为 error")
                    # 处理失败
                    self.current_workflow.update({
                        "status": "error",
                        "end_time": datetime.now().isoformat(),
                        "error": result["message"]
                    })
                    print(f"❌ 工作流 {task_id} 处理失败: {result['message']}")
                    self._archive_workflow()

                print(f"📊 更新后的工作流状态: {self.current_workflow}")

        except Exception as e:
            print(f"❌ _process_workflow 异常: {str(e)}")
            import traceback
            print(f"❌ 异常堆栈: {traceback.format_exc()}")

            error_msg = f"工作流处理异常: {str(e)}"

            print("🔒 获取工作流锁以设置错误状态...")
            with self._workflow_lock:
                self.current_workflow.update({
                    "status": "error",
                    "end_time": datetime.now().isoformat(),
                    "error": error_msg
                })
                print(f"❌ 工作流状态已设置为错误: {self.current_workflow}")
                self._archive_workflow()

        print("🔄 _process_workflow 后台线程结束")

    def submit_workflow_url(self, task_id: str, result_url: str):
        """提交工作流结果URL"""
        with self._workflow_lock:
            if (self.current_workflow["task_id"] != task_id or
                self.current_workflow["status"] != "waiting_url"):
                return {
                    "success": False,
                    "message": f"任务 {task_id} 不存在或状态不正确"
                }

            # 更新工作流状态
            self.current_workflow.update({
                "status": "completed",
                "end_time": datetime.now().isoformat(),
                "result_url": result_url
            })

            self.logger.info(f"工作流 {task_id} 完成，URL: {result_url}")

            # 如果有回调URL，发送通知
            callback_url = self.current_workflow.get("callback_url")
            if callback_url:
                self._send_callback_notification(callback_url, task_id, result_url)

            # 归档工作流
            self._archive_workflow()

            return {
                "success": True,
                "message": "工作流已完成",
                "task_id": task_id,
                "result_url": result_url
            }

    def get_workflow_status(self, task_id: str = None):
        """获取工作流状态"""
        with self._workflow_lock:
            if task_id is None:
                # 返回当前工作流状态
                return dict(self.current_workflow)

            # 检查当前工作流
            if self.current_workflow["task_id"] == task_id:
                return dict(self.current_workflow)

            # 检查历史记录
            for workflow in self.workflow_history:
                if workflow["task_id"] == task_id:
                    return dict(workflow)

            return {
                "task_id": task_id,
                "status": "not_found",
                "message": "任务不存在"
            }

    def _archive_workflow(self):
        """归档当前工作流到历史记录"""
        if self.current_workflow["task_id"]:
            # 添加到历史记录
            self.workflow_history.append(dict(self.current_workflow))

            # 保持历史记录不超过10个
            if len(self.workflow_history) > 10:
                self.workflow_history.pop(0)

            # 重置当前工作流
            self.current_workflow = {
                "task_id": None,
                "status": "idle",
                "start_time": None,
                "end_time": None,
                "result_url": None,
                "error": None,
                "user_token": None,
                "report_type": None,
                "callback_url": None
            }

    def _send_callback_notification(self, callback_url: str, task_id: str, result_url: str):
        """发送回调通知"""
        try:
            payload = {
                "task_id": task_id,
                "status": "completed",
                "result_url": result_url,
                "timestamp": datetime.now().isoformat()
            }

            response = requests.post(
                callback_url,
                json=payload,
                timeout=10,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code == 200:
                self.logger.info(f"回调通知发送成功: {callback_url}")
            else:
                self.logger.warning(f"回调通知失败: {callback_url}, 状态码: {response.status_code}")

        except Exception as e:
            self.logger.error(f"发送回调通知异常: {callback_url}, 错误: {str(e)}")

    def complete_workflow(self, task_id: str):
        """标记工作流完成（用于回调接口）"""
        with self._workflow_lock:
            if (self.current_workflow["task_id"] == task_id and
                self.current_workflow["status"] in ["processing", "waiting_url"]):

                self.current_workflow.update({
                    "status": "completed",
                    "end_time": datetime.now().isoformat()
                })

                self.logger.info(f"工作流 {task_id} 已标记为完成")
                self._archive_workflow()
                return True

            return False

    def is_workflow_busy(self):
        """检查工作流是否忙碌"""
        return self.current_workflow["status"] in ["processing", "waiting_url"]









def create_app():
    """创建Flask应用"""
    print("🔧 创建Flask应用...")

    try:
        print("   📝 初始化Flask实例")
        app = Flask(__name__)
        print("   ✅ Flask实例创建成功")

        print("   📝 配置CORS")
        CORS(app)
        print("   ✅ CORS配置完成")

        print("   📝 配置日志系统")
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler("workflow_manager.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        print("   ✅ 日志系统配置完成")

        print("   📝 获取TaskManager单例")
        # 获取单例实例
        task_manager = TaskManager.get_instance()
        print("   ✅ TaskManager单例获取成功")

        print("   📝 创建ImageProcessingService实例")
        image_service = ImageProcessingService()
        print("   ✅ ImageProcessingService实例创建成功")

    except Exception as e:
        print(f"   ❌ Flask应用初始化失败: {e}")
        raise

    print("   📝 注册API路由...")

    @app.route('/test', methods=['GET', 'POST'])
    def test_route():
        """测试路由 - 验证服务是否运行"""
        print("🔥🔥🔥 /test 路由被访问了！🔥🔥🔥")
        return "🔥 服务正在运行！当前时间: " + datetime.now().isoformat()

    @app.route('/api/server-status', methods=['GET'])
    def server_status():
        """简化的服务器状态接口"""
        if task_manager.is_workflow_busy():
            return jsonify({"status": "busy"})
        else:
            return jsonify({"status": "idle"})

    @app.route('/api/assign-server', methods=['POST'])
    def assign_server():
        """简化的服务器分配接口"""
        if request.headers.get('X-API-KEY') != Config.API_KEY:
            return jsonify({"status": "error", "message": "Invalid API Key"}), 401

        if task_manager.is_workflow_busy():
            return jsonify({
                "status": "error",
                "message": "工作流正在处理中",
                "timestamp": datetime.now().isoformat()
            }), 503
        else:
            return jsonify({
                "status": "success",
                "assignedServer": "134.175.126.100:9696",  # 单例模式，固定返回本地
                "timestamp": datetime.now().isoformat()
            })

    @app.route('/api/report', methods=['POST'])
    def handle_report():
        """处理报告请求"""
        print("🔥🔥🔥🔥🔥 /api/report 接口被调用了！！！ 🔥🔥🔥🔥🔥")
        print(f"🔥🔥🔥 时间: {datetime.now()} 🔥🔥🔥")
        print("\n" + "="*50)
        print("🔥 /api/report 接口被调用")
        print("="*50)

        try:
            print("📋 步骤1: 检查请求头")
            print(f"   Content-Type: {request.headers.get('Content-Type')}")
            print(f"   User-Agent: {request.headers.get('User-Agent')}")
            print(f"   X-API-KEY: {request.headers.get('X-API-KEY')}")
            print(f"   X-User-Token: {request.headers.get('X-User-Token')}")

            # API密钥验证
            api_key = request.headers.get('X-API-KEY')
            print(f"📋 步骤2: API密钥验证")
            print(f"   接收到的密钥: {api_key}")
            print(f"   期望的密钥: {Config.API_KEY}")
            print(f"   验证结果: {'✅ 通过' if api_key == Config.API_KEY else '❌ 失败'}")

            if api_key != Config.API_KEY:
                print("❌ API密钥验证失败，返回401")
                return jsonify({"status": "error", "message": "无效的API密钥"}), 401

            print("📋 步骤3: 解析请求数据")
            # 获取请求数据
            try:
                data = request.get_json()
                print(f"   原始数据: {data}")
                print(f"   数据类型: {type(data)}")
                if data:
                    print(f"   数据键: {list(data.keys())}")
                else:
                    print("   ❌ 数据为空")
            except Exception as e:
                print(f"   ❌ JSON解析失败: {e}")
                data = None

            if not data:
                print("❌ 请求体为空，返回400")
                return jsonify({
                    "status": "error",
                    "message": "请求体不能为空",
                    "timestamp": datetime.now().isoformat()
                }), 400

            print("📋 步骤4: 验证必填字段")
            # 验证必填字段
            required_fields = ['task_id', 'report_type']
            for field in required_fields:
                if field not in data:
                    print(f"   ❌ 缺少字段: {field}")
                    return jsonify({
                        "status": "error",
                        "message": f"缺少必填字段: {field}",
                        "timestamp": datetime.now().isoformat()
                    }), 400
                else:
                    print(f"   ✅ 字段存在: {field} = {data[field]}")

            print("📋 步骤5: 提取业务参数")
            # 获取用户令牌和回调URL
            user_token = request.headers.get('X-User-Token', '')
            callback_url = data.get('callback_url', '')
            request_data = data.get('requestData', {})

            print(f"   task_id: {data['task_id']}")
            print(f"   report_type: {data['report_type']}")
            print(f"   user_token: {user_token}")
            print(f"   callback_url: {callback_url}")
            print(f"   requestData: {request_data}")

            print("📋 步骤6: 调用TaskManager.start_workflow")
            # 启动工作流
            try:
                result = task_manager.start_workflow(
                    task_id=data['task_id'],
                    report_type=data['report_type'],
                    data=data,
                    user_token=user_token,
                    callback_url=callback_url
                )
                print(f"   工作流启动结果: {result}")
            except Exception as e:
                print(f"   ❌ start_workflow调用失败: {e}")
                import traceback
                print(f"   错误堆栈: {traceback.format_exc()}")
                raise

            print("📋 步骤7: 构造响应")
            if result["success"]:
                response_data = {
                    "status": "processing",
                    "task_id": result["task_id"],
                    "message": result["message"],
                    "timestamp": datetime.now().isoformat()
                }
                print(f"   ✅ 成功响应: {response_data}")
                return jsonify(response_data)
            else:
                response_data = {
                    "status": "error",
                    "message": result["message"],
                    "current_task_id": result.get("current_task_id"),
                    "timestamp": datetime.now().isoformat()
                }
                print(f"   ❌ 错误响应: {response_data}")
                return jsonify(response_data), 409

        except Exception as e:
            print(f"❌ 接口处理异常: {str(e)}")
            import traceback
            print(f"❌ 异常堆栈: {traceback.format_exc()}")
            return jsonify({
                "status": "error",
                "message": f"服务器内部错误: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }), 500
        finally:
            print("="*50)
            print("🔥 /api/report 接口处理完成")
            print("="*50)

    @app.route('/api/submit-result', methods=['POST'])
    def submit_result():
        """提交工作流结果"""
        if request.headers.get('X-API-KEY') != Config.API_KEY:
            return jsonify({
                "status": "error",
                "message": "无效的API密钥",
                "timestamp": datetime.now().isoformat()
            }), 401

        data = request.get_json()
        if not data or 'task_id' not in data or 'result_url' not in data:
            return jsonify({
                "status": "error",
                "message": "缺少必要参数",
                "timestamp": datetime.now().isoformat()
            }), 400

        result = task_manager.submit_workflow_url(data['task_id'], data['result_url'])

        if result["success"]:
            return jsonify({
                "status": "success",
                "message": result["message"],
                "task_id": result["task_id"],
                "result_url": result["result_url"],
                "timestamp": datetime.now().isoformat()
            })
        else:
            return jsonify({
                "status": "error",
                "message": result["message"],
                "timestamp": datetime.now().isoformat()
            }), 404

    @app.route('/api/workflow-complete', methods=['POST'])
    def workflow_complete():
        """工作流完成回调接口"""
        if request.headers.get('X-API-KEY') != Config.API_KEY:
            return jsonify({
                "status": "error",
                "message": "无效的API密钥",
                "timestamp": datetime.now().isoformat()
            }), 401

        data = request.get_json()
        if not data or 'task_id' not in data:
            return jsonify({
                "status": "error",
                "message": "缺少task_id参数",
                "timestamp": datetime.now().isoformat()
            }), 400

        if task_manager.complete_workflow(data['task_id']):
            return jsonify({
                "status": "success",
                "message": "工作流已标记为完成",
                "task_id": data['task_id'],
                "timestamp": datetime.now().isoformat()
            })
        else:
            return jsonify({
                "status": "error",
                "message": "工作流不存在或状态不正确",
                "timestamp": datetime.now().isoformat()
            }), 404

    @app.route('/api/status', methods=['GET'])
    def get_status():
        """获取工作流状态"""
        task_id = request.args.get('task_id')
        status = task_manager.get_workflow_status(task_id)

        return jsonify({
            "status": "success",
            "workflow": status,
            "timestamp": datetime.now().isoformat()
        })

    @app.route('/api/current-task', methods=['GET'])
    def get_current_task():
        """获取当前任务（兼容旧接口）"""
        task_id = request.args.get('task_id')
        workflow_status = task_manager.get_workflow_status(task_id)

        # 转换为旧格式
        if workflow_status["status"] == "not_found":
            current_task = None
            last_task = {"task_id": task_id, "status": "not_found"}
        elif workflow_status["status"] in ["processing", "waiting_url"]:
            current_task = {
                "task_id": workflow_status["task_id"],
                "status": workflow_status["status"],
                "start_time": workflow_status["start_time"]
            }
            last_task = workflow_status
        else:
            current_task = None
            last_task = workflow_status

        return jsonify({
            "status": "success",
            "current_task": current_task,
            "last_task": last_task,
            "pending_tasks": 1 if workflow_status["status"] in ["processing", "waiting_url"] else 0,
            "timestamp": datetime.now().isoformat()
        })

    @app.route('/health', methods=['GET'])
    def health_check():
        """健康检查"""
        return jsonify({
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0",
            "system": "Workflow Manager (Singleton Mode)"
        })

    print("   ✅ 所有API路由注册完成")
    route_count = len(list(app.url_map.iter_rules()))
    print(f"   📊 总共注册了 {route_count} 个路由")

    # 检查关键路由
    key_routes = ['/api/report', '/api/status', '/health']
    for route in key_routes:
        found = any(route in str(rule) for rule in app.url_map.iter_rules())
        status = "✅" if found else "❌"
        print(f"   {status} 关键路由: {route}")

    print("   ✅ Flask应用创建完成")
    return app


print("🔥🔥🔥 Python脚本开始执行 🔥🔥🔥")
print(f"当前时间: {datetime.now()}")
print(f"Python版本: {os.sys.version}")
print(f"脚本路径: {__file__}")

if __name__ == '__main__':
    print("🔥🔥🔥 进入main函数 🔥🔥🔥")
    print("="*60)
    print("🚀 工作流管理器启动调试信息")
    print("="*60)

    try:
        print("📋 步骤1: 检查Python环境")
        print(f"   Python版本: {os.sys.version}")
        print(f"   当前工作目录: {os.getcwd()}")
        print(f"   脚本路径: {__file__}")

        print("\n📋 步骤2: 检查依赖导入")
        print(f"   HAS_IMAGE_PROCESSING: {HAS_IMAGE_PROCESSING}")

        print("\n📋 步骤3: 检查环境变量")
        print(f"   PORT: {os.getenv('PORT', '7000')}")
        print(f"   API_KEY: {os.getenv('API_KEY', 'shadowbot2023')}")
        print(f"   SERVER_ID: {os.getenv('SERVER_ID', socket.gethostname())}")

        print("\n📋 步骤4: 创建必要目录")
        try:
            os.makedirs(Config.SCREENSHOT_DIR, exist_ok=True)
            print(f"   ✅ 截图目录: {Config.SCREENSHOT_DIR}")
        except Exception as e:
            print(f"   ❌ 截图目录创建失败: {e}")

        try:
            os.makedirs(Config.TEMPLATE_DIR, exist_ok=True)
            print(f"   ✅ 模板目录: {Config.TEMPLATE_DIR}")
        except Exception as e:
            print(f"   ❌ 模板目录创建失败: {e}")

        print("\n📋 步骤5: 初始化TaskManager单例")
        try:
            task_manager = TaskManager.get_instance()
            print("   ✅ TaskManager单例初始化成功")
        except Exception as e:
            print(f"   ❌ TaskManager单例初始化失败: {e}")
            raise

        print("\n📋 步骤6: 创建Flask应用")
        try:
            app = create_app()
            print("   ✅ Flask应用创建成功")
        except Exception as e:
            print(f"   ❌ Flask应用创建失败: {e}")
            raise

        print("\n📋 步骤7: 检查路由注册")
        route_count = 0
        for rule in app.url_map.iter_rules():
            route_count += 1
            if '/api/report' in str(rule):
                print(f"   ✅ 关键路由: {rule}")
        print(f"   总路由数: {route_count}")

        print("\n📋 步骤8: 准备启动服务")
        port = int(os.getenv("PORT", 7000))
        print(f"   监听地址: 0.0.0.0:{port}")
        print(f"   API密钥: {Config.API_KEY}")
        print(f"   服务器ID: {Config.SERVER_ID}")
        print(f"   运行模式: {'完整UI自动化' if HAS_IMAGE_PROCESSING else 'API服务+模拟模式'}")

        print("\n" + "="*60)
        print("🌐 正在启动Waitress服务器...")
        print(f"🔥🔥🔥 即将调用 serve(app, host='0.0.0.0', port={port}) 🔥🔥🔥")
        print("="*60)

        # 启动服务
        print(f"🔥🔥🔥 开始监听端口 {port}... 🔥🔥🔥")
        serve(app, host="0.0.0.0", port=port)
        print("🔥🔥🔥 serve函数返回了（这通常不应该发生）🔥🔥🔥")

    except ImportError as e:
        print(f"\n❌ 导入错误: {str(e)}")
        print("💡 可能缺少Python依赖包，请安装:")
        print("   pip3 install flask flask-cors waitress requests")
        print("   可选: pip3 install opencv-python numpy pillow pyautogui")
        exit(1)

    except Exception as e:
        print(f"\n❌ 服务启动失败: {str(e)}")
        print("\n🔍 详细错误信息:")
        import traceback
        traceback.print_exc()

        print("\n💡 常见解决方案:")
        print("1. 检查端口是否被占用: lsof -i :7000")
        print("2. 检查Python依赖: pip3 list | grep -E 'flask|waitress'")
        print("3. 检查文件权限: ls -la new_workflow_manager.py")
        print("4. 尝试不同端口: PORT=8000 python3 new_workflow_manager.py")
        exit(1)
